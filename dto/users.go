package dto

import "time"

const (
	TypeOfPhone = 0
	TypeOfEmail = 1
)

type Users struct {
	UserID    string `json:"user_id" bson:"user_id"`
	CreatedAt int64  `json:"created_at" bson:"created_at"`
	UpdatedAt int64  `json:"updated_at" bson:"updated_at"`
	Username  string `json:"username" bson:"username"`
}

type UserLogin struct {
	UserID string `json:"user_id" bson:"user_id"`

	ProductID  string `json:"product_id" bson:"product_id"`
	Object     string `json:"object" bson:"object"` // 手机号/邮箱
	Type       int8   `json:"type" bson:"type"`     //0:手机号 1:邮箱
	SourceType string `json:"source_type" bson:"source_type"`
	CreatedAt  int64  `json:"created_at" bson:"created_at"`
	UpdatedAt  int64  `json:"updated_at" bson:"updated_at"`
	State      int32  `json:"state" bson:"state"`
}

type UserTokenData struct {
	UserID    string `json:"user_id" bson:"user_id"`
	ProductID string `json:"product_id" bson:"product_id"`
	ClientID  string `json:"client_id" bson:"client_id"`
	Token     string `json:"token" bson:"token"`

	ExpiredAt time.Time `json:"expired_at" bson:"expired_at"`
	CreatedAt int64     `json:"created_at" bson:"created_at"`
	UpdatedAt int64     `json:"updated_at" bson:"updated_at"`
}

type UserLoginRecordStruct struct {
	Phone       string `json:"phone"`
	Email       string `json:"email"`
	ProductID   string `json:"product_id"`
	ClientID    string `json:"client_id"`
	Token       string `json:"token"`
	ExpiratedAt int    `json:"expirated_at"`
}
