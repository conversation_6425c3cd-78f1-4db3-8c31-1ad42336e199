package dto

type AppListRequest struct {
	UserCode string `json:"user_code"`
	Page     int    `json:"page"`
	Limit    int    `json:"limit"`
}

type AppTagItem struct {
	TagCode      string `json:"tag_code" bson:"tag_code"`
	CategoryCode string `json:"category_code" bson:"category_code"`
}

type AppItem struct {
	AppCode string       `json:"app_code" bson:"app_code"`
	AppName string       `json:"app_name" bson:"app_name"`
	AppType string       `json:"app_type" bson:"app_type"`
	AppTags []AppTagItem `json:"app_tags" bson:"app_tags"`
}

type AppListResponse struct {
	Total int64     `json:"total"`
	Items []AppItem `json:"items"`
}

type AppTag struct {
	TagCode string `json:"tag_code" bson:"tag_code"`
	TagName string `json:"tag_name" bson:"tag_name"`
}

type PortalConfig struct {
	ProductID string  `json:"product_id" bson:"product_id"`
	Configs   Configs `json:"configs" bson:"configs"`
	CreatedAt int     `json:"created_at" bson:"created_at"`
	UpdatedAt int     `json:"updated_at" bson:"updated_at"`
}

type Configs struct {
	SubDomain                 string `json:"sub_domain" bson:"sub_domain"`
	Domain                    string `json:"domain" bson:"fixed_domain"`
	UnauthorizedPromptContent string `json:"unauthorized_prompt_content" bson:"unauthorized_prompt_content"`
	LoginValidityDays         int    `json:"login_validity_days" bson:"login_validity_days"`
	DisplayConfig             string `json:"display_config" bson:"display_config"`
}

type SSORequest struct {
	Email      string `json:"email" validate:"required"`
	OrgCode    string `json:"org_code" validate:"required"`
	ProductID  string `json:"product_id" validate:"required"`
	ClientID   string
	ExpireTime int64 `json:"expire_time" `
}
