// Package dto define auth info
package dto

import (
	"encoding/json"
	"time"
)

type AuthUserInfo struct {
	ProductID     string `json:"product_id" bson:"product_id"`
	OrgCode       string `json:"org_code" bson:"org_code"`
	UserCode      string `json:"user_code" bson:"user_code"`
	Username      string `json:"username" bson:"user_name"`
	Email         string `json:"email,omitempty" bson:"email"`
	Phone         string `json:"phone,omitempty" bson:"mobile"`
	OrgName       string `json:"org_name,omitempty" bson:"org_name"`
	PasswordReset int    `bson:"password_reset" json:"password_reset"`
	ClientID      string `json:"client_id" bson:"client_id"`
}

const (
	UserStatusNormal     = 0 // 0:正常
	UserStatusPause      = 1 // 1:暂停使用
	UserStatusLocked     = 2 // 2:锁定
	UserStatusDelete     = 3 // 3:删除
	UserPasswordReset    = 1 // 1 需要重置密码
	UserPasswordNotReset = 2 // 2 不需要重置密码
)

// UserInfo 用户信息
type UserInfo struct {
	ProductID     string    `json:"product_id" bson:"product_id"`
	OrgCode       string    `json:"org_code" bson:"org_code"`
	UserCode      string    `json:"user_code" bson:"user_code"`
	Username      string    `json:"username" bson:"user_name"`
	Email         string    `json:"email,omitempty" bson:"email"`
	Phone         string    `json:"phone,omitempty" bson:"mobile"`
	OrgName       string    `json:"org_name,omitempty" bson:"org_name"`
	PasswordHash  string    `json:"password_hash,omitempty" bson:"password"`
	Status        int64     `json:"status" bson:"status"`                 // 0:正常 1:暂停使用 2:锁定 3:删除
	PasswordReset int       `json:"password_reset" bson:"password_reset"` // 1 需要重置密码 2-不需要重置密码
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	// 第三方登录相关信息
	ThirdPartyInfo map[string]interface{} `json:"third_party_info,omitempty"`
}

// TokenResponse OAuth2.0 令牌响应
type TokenResponse struct {
	AccessToken   string `json:"access_token"`
	RefreshToken  string `json:"refresh_token"`
	TokenType     string `json:"token_type"`
	ExpiresIn     int    `json:"expires_in"`
	UserCode      string `json:"user_code"`
	Username      string `json:"username"`
	OrgCode       string `json:"org_code"`
	ProductID     string `json:"product_id"`
	OrgName       string `json:"org_name"`
	PasswordReset int    `json:"password_reset"`
	LoginType     string `json:"login_type"`
	Ticket        string `json:"ticket"`
	Domain        string `json:"url"`
	Email         string `json:"email"`
}

// OAuthAuthorizeRequest OAuth2.0 授权请求
type OAuthAuthorizeRequest struct {
	ResponseType string `json:"response_type"`
	ClientID     string `json:"client_id"`
	RedirectURI  string `json:"redirect_uri"`
	Scope        string `json:"scope"`
	State        string `json:"state"`
}

// OAuthTokenRequest OAuth2.0 令牌请求
type OAuthTokenRequest struct {
	GrantType    string `json:"grant_type"`
	Code         string `json:"code,omitempty"`
	RedirectURI  string `json:"redirect_uri,omitempty"`
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	RefreshToken string `json:"refresh_token,omitempty"`
	Username     string `json:"username,omitempty"`
	Password     string `json:"password,omitempty"`
}

// LoginRequest 登录请求
type LoginRequest struct {
	OrgCode   string `json:"org_code" binding:"required"`
	ProductID string `json:"product_id" binding:"required"`
	Email     string `json:"email"` // 保留
	Account   string `json:"account" binding:"required"`
	Password  string `json:"password" binding:"required"`
	Ticket    string `json:"ticket" binding:"required"`
	RandStr   string `json:"randstr" binding:"required"`
}

// PhoneLoginRequest 手机号验证码登录请求
type PhoneLoginRequest struct {
	OrgCode   string `json:"org_code" binding:"required"`
	ProductID string `json:"product_id" binding:"required"`
	Phone     string `json:"phone" binding:"required"`
	Code      string `json:"code" binding:"required"`
	Ticket    string
	ClientID  string
}

// VerificationCodeLoginRequest 通用验证码登录请求
type VerificationCodeLoginRequest struct {
	OrgCode   string `json:"org_code" binding:"required"`
	ProductID string `json:"product_id" binding:"required"`
	Phone     string `json:"phone"` // 手机号（与email二选一）
	Email     string `json:"email"` // 邮箱（与phone二选一）
	Code      string `json:"code" binding:"required"`
	Ticket    string
	ClientID  string
}
type LdapLoginRequest struct {
	OrgCode   string `json:"org_code" binding:"required"`
	ProductID string `json:"product_id" binding:"required"`
	Account   string `json:"account" binding:"required"`
	Password  string `json:"password" binding:"required"`
	ClientID  string
}

// ThirdPartyLoginRequest 第三方登录请求
type ThirdPartyLoginRequest struct {
	Provider string `json:"provider"`
	Code     string `json:"code"`
}

type CaptchaRequest struct {
	Phone     string `json:"phone"`
	Email     string `json:"email"`
	OrgCode   string `json:"org_code" binding:"required"`
	ProductID string `json:"product_id" binding:"required"`
	RandStr   string `json:"randstr" binding:"required"`
	Ticket    string `json:"ticket" binding:"required"`                                     // 验证码的唯一标识
	Source    string `json:"source" binding:"required,oneof=login reset_password register"` // login or register /reset_password
}

type Source string

const (
	Login         Source = "login"
	Register      Source = "register"
	ResetPassword Source = "reset_password"
)

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token"`
}

// TokenValidationResponse 定义token验证响应
type TokenValidationResponse struct {
	Status string        `json:"status"`
	User   *AuthUserInfo `json:"user"`
}

// UnmarshalUserInfo 反序列化用户信息
func UnmarshalUserInfo(data string) (*AuthUserInfo, error) {
	user := &AuthUserInfo{}
	err := json.Unmarshal([]byte(data), user)
	return user, err
}

type GetUserInfoByPhoneOrEmail struct {
	OrgCode   string
	Account   string
	Phone     string
	Email     string
	Pwd       string
	ProductID string
	Ticket    string
	ClientID  string
}

type ResetPwdReq struct {
	Phone           string `json:"phone" binding:"required"`
	Code            string `json:"code" binding:"required"`
	Password        string `json:"password" binding:"required"`
	ConfirmPassword string `json:"confirm_password" binding:"required,eqfield=Password"`
	OrgCode         string `json:"org_code" binding:"required"`
	ProductID       string `json:"product_id" binding:"required"`
	ChangePWd       bool
}

type AuthenticateRequest struct {
	ProductID        string `json:"product_id" binding:"required"`
	Pwd              string `json:"password"`
	AuthenticateType string `json:"authenticate_type" binding:"required"` //  企业密码校验 password_check, //status_check
}

type LoginResetPwd struct {
	Password        string `json:"password" binding:"required"`
	ConfirmPassword string `json:"confirm_password" binding:"required,eqfield=Password"`
}

const (
	// PasswordCheck string = "password_check"
	StatusCheck string = "status_check"
)

type LoginAccountIP struct {
	Account   string
	IP        string
	ProductID string
	OrgCode   string
}

type LdapDomainAccountLoginRequest struct {
	OrgCode    string `json:"org_code" `
	ProductID  string `json:"product_id" binding:"required"`
	Account    string `json:"account" binding:"required"`
	InDomain   bool   `json:"in_domain"`
	DomainName string `json:"domain_name"`
	Ticket     string `json:"ticket"`
	ClientID   string
}

type GuestLoginRequest struct {
	OrgCode   string `json:"org_code"`
	ProductID string `json:"product_id" binding:"required"`
	Ticket    string `json:"ticket"`
}

type LoginTypeListRequest struct {
	OrgCode   string
	ProductID string `json:"product_id" binding:"required"`
}

type LoginTypeListInfo struct {
	Type       string `json:"type"`
	ObjectCode string `json:"object_code"`
	Name       string `json:"name"`
}

type LoginTypeListResponse struct {
	Items []LoginTypeListInfo `json:"items"`
}

type ThirdPartyRequest struct {
	Provider string
	OrgCode  string
	Code     string
	Ticket   string
	ClientID string
}
