package main

import (
	"context"
	"errors"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"ztna_server_auth/common/config"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/controller"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure"
	"ztna_server_auth/infrastructure/client"
	"ztna_server_auth/service"

	"github.com/gin-gonic/gin"
)

type Servers struct {
	ginEngine  *gin.Engine
	httpServer *http.Server
}

func NewServer(conf *config.AppConfig) *Servers {

	// 初始化service
	service.Init(conf)

	// 创建 Gin 引擎
	gin.SetMode(gin.DebugMode)
	engine := gin.Default()

	// 注册认证路由
	controller.RegisterRoutes(engine)

	// 创建 HTTP 服务器
	httpServer := &http.Server{
		Addr:    conf.Service.Addr,
		Handler: engine,
	}

	return &Servers{
		ginEngine:  engine,
		httpServer: httpServer,
	}
}

func (s *Servers) Start() {
	// 启动 HTTP 服务器
	go func() {
		if err := s.httpServer.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			logs.PErrorf("HTTP server error: %v", err)
		}
	}()

	logs.PInfof("HTTP server started on %s", s.httpServer.Addr)

	// 设置优雅关闭
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// 关闭服务器
	s.Close(dto.ServerStopChanMessage{Message: "收到终止信号"})
}

func (s *Servers) Close(sm dto.ServerStopChanMessage) {
	logs.PErrorf("[关闭] - 信号： - %s\n", sm.Message)

	// 关闭 HTTP 服务器
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := s.httpServer.Shutdown(ctx); err != nil {
		logs.PErrorf("HTTP server shutdown error: %v", err)
	}

	// 停止离线检查器和日志服务
	if service.LoginAccessService != nil {
		service.LoginAccessService.StopOfflineChecker()
	}

	// 关闭 Redis 连接
	_ = client.Close()
}

func main() {
	conf := config.Config()
	logs.Init(logs.DefaultConfig())
	infrastructure.Init(conf)

	logs.PInfof("[启动] - %s ( %s ) %s\n", conf.Service.Name, conf.Service.Version, conf.Service.Mode)

	server := NewServer(conf)
	server.Start()
}
