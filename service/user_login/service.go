package user_login

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"time"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/common/utils"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure/client"
)

type Service interface {
	RecordUserLoginData(ctx context.Context, req *dto.UserLoginRecordStruct) error
	InsertOrUpdateUserToken(ctx context.Context, req *dto.UserTokenData) error
}

type userLogin struct {
	usersColl, userToken, userLoginColl, organizationUserCollection *mongo.Collection
}

func NewUserLogin() *userLogin {

	return &userLogin{
		usersColl:                  client.Mongo.Database.Collection("users"),
		userToken:                  client.Mongo.Database.Collection("users_token"),
		userLoginColl:              client.Mongo.Database.Collection("users_login"),
		organizationUserCollection: client.Mongo.Database.Collection("organization_users"),
	}

}

func (l *userLogin) RecordUserLoginData(ctx context.Context, req *dto.UserLoginRecordStruct) error {

	if req.Phone == "" && req.Email == "" {
		logs.Warnf("this login user iphone and email is empty")
		return nil
	}

	orgUsers, err := l.getOrgUserInfo(ctx, req.Phone, req.Email)
	if err != nil {
		logs.Warnf("getOrgUserInfo failed: %v", err)
		return err
	}

	if len(orgUsers) == 0 {
		logs.Warnf("orgUsers is empty")
		return nil
	}

	var userLogin = make([]*dto.UserLogin, 0, len(orgUsers))
	// 处理数据
	userId := utils.GenerateUserCode()

	for _, orgUser := range orgUsers {
		if orgUser.Mobile != "" {
			userLogin = append(userLogin, &dto.UserLogin{
				UserID:    userId,
				ProductID: orgUser.ProductID,
				Object:    orgUser.Mobile,
				Type:      dto.TypeOfPhone,
				State:     1,
			})
		}
		if orgUser.Email != "" {
			userLogin = append(userLogin, &dto.UserLogin{
				UserID:    userId,
				ProductID: orgUser.ProductID,
				Object:    orgUser.Email,
				Type:      dto.TypeOfEmail,
				State:     1,
			})
		}

	}

	insertLoginErr := l.insertUserLoginData(ctx, userLogin)
	if insertLoginErr != nil {
		logs.Warnf("failed to call insertUserLoginData error: %s", err)
	}

	userName := orgUsers[0].UserName

	userInfo, err := l.InsertOrUpdateData(ctx, &dto.Users{
		UserID:   userId,
		Username: userName,
	})

	if err != nil {
		logs.Warnf("failed to call InsertOrUpdateData ", err)
	}

	logs.Infof("userInfo: %v", userInfo)

	InsertUserTokenErr := l.InsertOrUpdateUserToken(ctx, &dto.UserTokenData{
		UserID:    userInfo.UserID,
		ProductID: req.ProductID,
		ClientID:  req.ClientID,
		Token:     req.Token,
		ExpiredAt: time.Now().Add(time.Duration(req.ExpiratedAt) * time.Second),
	})
	if InsertUserTokenErr != nil {
		logs.Warnf("failed to call InsertOrUpdateUserToken error: %s", InsertUserTokenErr)
	}

	return nil

}

func (l *userLogin) getOrgUserInfo(ctx context.Context, phone, email string) ([]*dto.OrganizationUser, error) {

	var orgUsers = make([]*dto.OrganizationUser, 0)

	filter := bson.M{}

	if phone != "" {
		filter["phone"] = phone
	}
	if email != "" {
		filter["email"] = email
	}

	cursor, err := l.organizationUserCollection.Find(ctx, filter)
	if err != nil {
		logs.Warnf("failed to call Find orgUser filter: %+v , error: %s", filter, err)
		return nil, err
	}

	if decodeErrr := cursor.All(ctx, &orgUsers); decodeErrr != nil {
		logs.Warnf("failed to call All Decode error %s", err)
		return nil, decodeErrr
	}

	return orgUsers, nil

}

func (l *userLogin) InsertOrUpdateData(ctx context.Context, req *dto.Users) (*dto.Users, error) {

	var baseUserInfo = &dto.Users{}

	filter := bson.D{
		{"user_id", req.UserID},
	}

	updateValue := bson.D{
		{"$set", bson.D{
			{"updated_at", time.Now().Unix()},
			{"username", req.Username},
		}},
		{"$setOnInsert", bson.D{
			{"created_at", time.Now().Unix()},
		}},
	}
	if err := l.usersColl.FindOneAndUpdate(ctx, filter, updateValue, options.FindOneAndUpdate().SetUpsert(true)).Decode(&baseUserInfo); err != nil {
		logs.Warnf("failed to FindOneAndUpdate error %s,filter: %+v, update: %+v", err, filter, updateValue)
	}

	return baseUserInfo, nil

}

func (l *userLogin) insertUserLoginData(ctx context.Context, req []*dto.UserLogin) error {

	var wms = make([]mongo.WriteModel, 0, len(req))

	for _, value := range req {

		wms = append(wms, mongo.NewUpdateOneModel().
			SetFilter(bson.D{
				{"objet", value.Object},
				{"type", value.Type},
				{"source_type", value.SourceType},
				{"product_id", value.ProductID},
			}).
			SetUpdate(bson.D{
				{"$set", bson.D{
					{"updated_at", time.Now().Unix()},
					{"state", value.State},
				}},
				{"$setOnInsert", bson.D{
					{"created_at", time.Now().Unix()},
					{"user_id", value.UserID},
				}},
			}).SetUpsert(true),
		)

	}

	bulkWrite, err := l.userLoginColl.BulkWrite(ctx, wms, options.BulkWrite())
	if err != nil {
		logs.Warnf("failed to BulkWrite error %s", err)
	}

	logs.Infof("bulkWrite %+v", bulkWrite)

	return err

}

func (l *userLogin) InsertOrUpdateUserToken(ctx context.Context, req *dto.UserTokenData) error {

	filter := bson.D{
		{"user_id", req.UserID},
		{"client_id", req.ClientID},
		{"product_id", req.ProductID},
	}

	updateValue := bson.D{
		{"$set", bson.D{
			{"updated_at", time.Now().Unix()},
			{"token", req.Token},
			{"expired_at", req.ExpiredAt},
		}},
		{"$setOnInsert", bson.D{
			{"created_at", time.Now().Unix()},
		}},
	}

	_, err := l.userToken.UpdateOne(ctx, filter, updateValue, options.Update().SetUpsert(true))
	if err != nil {
		logs.Warnf("failed to UpdateOne error %s", err)
	}

	return nil

}
