package log_writer

import (
	"context"
	"fmt"
	"sync"
	"time"
	"ztna_server_auth/dto"
	"ztna_server_auth/infrastructure/client"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Service interface {
	WriteLog(log dto.LoginAccess)
	UpdateUserLoginState(state dto.UserLoginState)
	StartOfflineChecker(intervalMinutes int) // 启动离线状态检查器
	StopOfflineChecker()                     // 停止离线状态检查器
}

type LogWriter struct {
	coll                   *mongo.Collection
	userLoginColl, aggColl *mongo.Collection
	logsChan               chan dto.LoginAccess
	wg                     sync.WaitGroup
	quit                   chan struct{}

	// 离线状态检查器相关字段
	offlineChecker     *time.Ticker
	offlineCheckerQuit chan struct{}
	offlineWg          sync.WaitGroup
}

// NewLogWriter 初始化一个批量日志写入器
func NewLogWriter(bufferSize int) *LogWriter {
	lw := &LogWriter{
		coll:               client.Mongo.Database.Collection("login_access_log"),
		aggColl:            client.Mongo.Database.Collection("login_access_agg"),
		userLoginColl:      client.Mongo.Database.Collection("user_logon_info"),
		logsChan:           make(chan dto.LoginAccess, bufferSize),
		quit:               make(chan struct{}),
		offlineCheckerQuit: make(chan struct{}),
	}
	lw.start()
	return lw
}

func (lw *LogWriter) start() {
	lw.wg.Add(1)
	go func() {
		defer lw.wg.Done()

		ticker := time.NewTicker(1 * time.Second) // 每 1 秒 flush 一次
		defer ticker.Stop()

		batch := make([]*dto.LoginAccess, 0, 100) // 每批最多 100 条

		for {
			select {
			case log := <-lw.logsChan:
				batch = append(batch, &log)
				if len(batch) >= 100 { // 达到批量上限
					lw.flush(batch)
					batch = make([]*dto.LoginAccess, 0, 100) // 清空 slice
				}
			case <-ticker.C:
				if len(batch) > 0 {
					lw.flush(batch)
					batch = make([]*dto.LoginAccess, 0, 100) // 清空 slice
				}
			case <-lw.quit:
				// flush 剩余数据
				if len(batch) > 0 {
					lw.flush(batch)
					batch = make([]*dto.LoginAccess, 0, 100) // 清空 slice
				}
				return
			}
		}
	}()
}

func (lw *LogWriter) flush(batch []*dto.LoginAccess) {

	if len(batch) == 0 {
		return
	}

	var docs = make([]interface{}, 0, len(batch))
	for _, doc := range batch {
		docs = append(docs, doc)
	}
	// 记录数据表

	if _, err := lw.coll.InsertMany(context.Background(), docs); err != nil {
		fmt.Println("batch insert log error:", err)
	} else {
		// 插入成功后，更新聚合统计
		lw.aggLoginAccess(batch)
	}
	batch = make([]*dto.LoginAccess, 0, 100)
}
func (lw *LogWriter) aggLoginAccess(batch []*dto.LoginAccess) {
	// 统计需要更新的聚合数据
	userCodeAggMap := make(map[string]*dto.LoginAccessAgg)
	clientIdAggMap := make(map[string]*dto.LoginAccessAgg)

	// 收集需要统计的维度
	for _, log := range batch {
		// Type=1: 按 userCode 统计不重复的 clientId
		userKey := fmt.Sprintf("%s_%s_%s", log.OrgCode, log.ProductID, log.UserCode)
		if _, exists := userCodeAggMap[userKey]; !exists {
			userCodeAggMap[userKey] = &dto.LoginAccessAgg{
				OrgCode:   log.OrgCode,
				OrgName:   log.OrgName,
				ProductID: log.ProductID,
				Type:      1,
				Object:    log.UserCode,
			}
		}

		// Type=2: 按 clientId 统计不重复的 userCode
		clientKey := fmt.Sprintf("%s_%s_%s", log.OrgCode, log.ProductID, log.ClientID)
		if _, exists := clientIdAggMap[clientKey]; !exists {
			clientIdAggMap[clientKey] = &dto.LoginAccessAgg{
				OrgCode:   log.OrgCode,
				OrgName:   log.OrgName,
				ProductID: log.ProductID,
				Type:      2,
				Object:    log.ClientID,
			}
		}
	}

	// 更新 userCode 聚合统计
	for _, agg := range userCodeAggMap {
		lw.updateUserCodeAgg(agg)
	}

	// 更新 clientId 聚合统计
	for _, agg := range clientIdAggMap {
		lw.updateClientIdAgg(agg)
	}
}
func (lw *LogWriter) WriteLog(log dto.LoginAccess) {
	select {
	case lw.logsChan <- log:
	default:
		// 如果队列满了，丢弃或打印（可改成写文件做兜底）
		fmt.Println("log channel full, dropping log:", log)
	}
}

func (lw *LogWriter) Stop() {
	close(lw.quit)
	lw.wg.Wait()

	// 停止离线检查器
	lw.StopOfflineChecker()
}

// updateUserCodeAgg 更新按 userCode 统计的聚合数据（统计不重复的 clientId 数量）
func (lw *LogWriter) updateUserCodeAgg(agg *dto.LoginAccessAgg) {
	ctx := context.Background()

	// 统计该 userCode 对应的不重复 clientId 数量
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"org_code":   agg.OrgCode,
				"product_id": agg.ProductID,
				"user_code":  agg.Object,
			},
		},
		{
			"$group": bson.M{
				"_id": bson.M{
					"client_id": "$client_id",
				},
			},
		},
		{
			"$count": "distinct_count",
		},
	}

	cursor, err := lw.coll.Aggregate(ctx, pipeline)
	if err != nil {
		fmt.Printf("aggregate user code error: %v\n", err)
		return
	}
	defer cursor.Close(ctx)

	var result []bson.M
	if err := cursor.All(ctx, &result); err != nil {
		fmt.Printf("decode aggregate result error: %v\n", err)
		return
	}

	var distinctCount int64 = 0
	if len(result) > 0 {
		if count, ok := result[0]["distinct_count"].(int32); ok {
			distinctCount = int64(count)
		}
	}

	// 更新聚合表
	filter := bson.M{
		"org_code":   agg.OrgCode,
		"product_id": agg.ProductID,
		"type":       1,
		"object":     agg.Object,
	}

	update := bson.M{
		"$set": bson.M{
			"agg_count":  distinctCount,
			"updated_at": time.Now().Unix(),
		},
		"$setOnInsert": bson.M{
			"created_at": time.Now().Unix(),
			"org_name":   agg.OrgName,
		},
	}

	opts := options.Update().SetUpsert(true)
	if _, err := lw.aggColl.UpdateOne(ctx, filter, update, opts); err != nil {
		fmt.Printf("update user code agg error: %v\n", err)
	}
}

// updateClientIdAgg 更新按 clientId 统计的聚合数据（统计不重复的 userCode 数量）
func (lw *LogWriter) updateClientIdAgg(agg *dto.LoginAccessAgg) {
	ctx := context.Background()

	// 统计该 clientId 对应的不重复 userCode 数量
	pipeline := []bson.M{
		{
			"$match": bson.M{
				"org_code":   agg.OrgCode,
				"product_id": agg.ProductID,
				"client_id":  agg.Object,
			},
		},
		{
			"$group": bson.M{
				"_id": bson.M{
					"user_code": "$user_code",
				},
			},
		},
		{
			"$count": "distinct_count",
		},
	}

	cursor, err := lw.coll.Aggregate(ctx, pipeline)
	if err != nil {
		fmt.Printf("aggregate client id error: %v\n", err)
		return
	}
	defer cursor.Close(ctx)

	var result []bson.M
	if err := cursor.All(ctx, &result); err != nil {
		fmt.Printf("decode aggregate result error: %v\n", err)
		return
	}

	var distinctCount int64 = 0
	if len(result) > 0 {
		if count, ok := result[0]["distinct_count"].(int32); ok {
			distinctCount = int64(count)
		}
	}

	// 更新聚合表
	filter := bson.M{
		"org_code":   agg.OrgCode,
		"product_id": agg.ProductID,
		"type":       2,
		"object":     agg.Object,
	}

	update := bson.M{
		"$set": bson.M{
			"agg_count":  distinctCount,
			"updated_at": time.Now().Unix(),
		},
		"$setOnInsert": bson.M{
			"created_at": time.Now().Unix(),
			"org_name":   agg.OrgName,
		},
	}

	opts := options.Update().SetUpsert(true)
	if _, err := lw.aggColl.UpdateOne(ctx, filter, update, opts); err != nil {
		fmt.Printf("update client id agg error: %v\n", err)
	}
}

// UpdateUserLoginState 更新用户登录状态
// 当 UserCode、ProductId、OrgCode 确定时，更新其他字段
func (lw *LogWriter) UpdateUserLoginState(state dto.UserLoginState) {
	ctx := context.Background()

	// 设置过滤条件：根据 UserCode、ProductId、OrgCode 查找
	filter := bson.M{
		"user_code":  state.UserCode,
		"product_id": state.ProductId,
		"org_code":   state.OrgCode,
	}
	now := time.Now().Unix()
	updateValue := bson.M{
		"client_id":  state.ClientID,
		"state":      state.State,
		"ip":         state.Ip,
		"area":       state.Area,
		"platform":   state.Platform,
		"updated_at": now,
	}

	if state.LoginType != "" {
		updateValue["login_type"] = state.LoginType
		updateValue["login_time"] = state.LoginTime
	}

	// 设置更新内容
	update := bson.M{
		"$set": updateValue,
		"$setOnInsert": bson.M{
			"created_at": now,
		},
	}

	// 使用 upsert 选项：如果不存在则插入，存在则更新
	opts := options.Update().SetUpsert(true)

	if _, err := lw.userLoginColl.UpdateOne(ctx, filter, update, opts); err != nil {
		fmt.Printf("update user login state error: %v\n", err)
	}
}

// StartOfflineChecker 启动离线状态检查器
// intervalMinutes: 检查间隔时间（分钟）
func (lw *LogWriter) StartOfflineChecker(intervalMinutes int) {
	// 如果已经启动了，先停止
	lw.StopOfflineChecker()

	if intervalMinutes <= 0 {
		intervalMinutes = 30 // 默认30分钟检查一次
	}

	lw.offlineChecker = time.NewTicker(time.Duration(intervalMinutes) * time.Minute)
	lw.offlineWg.Add(1)

	go func() {
		defer lw.offlineWg.Done()

		for {
			select {
			case <-lw.offlineChecker.C:
				lw.updateOfflineUsers(intervalMinutes)
			case <-lw.offlineCheckerQuit:
				return
			}
		}
	}()

	fmt.Printf("Offline checker started with interval: %d minutes\n", intervalMinutes)
}

// StopOfflineChecker 停止离线状态检查器
func (lw *LogWriter) StopOfflineChecker() {
	if lw.offlineChecker != nil {
		lw.offlineChecker.Stop()
		close(lw.offlineCheckerQuit)
		lw.offlineWg.Wait()

		// 重新创建 channel 以便下次使用
		lw.offlineCheckerQuit = make(chan struct{})
		lw.offlineChecker = nil

		fmt.Println("Offline checker stopped")
	}
}

// updateOfflineUsers 更新离线用户状态
// thresholdMinutes: 超过多少分钟没有活动就认为离线
func (lw *LogWriter) updateOfflineUsers(thresholdMinutes int) {
	ctx := context.Background()

	// 计算阈值时间戳（当前时间 - 阈值分钟数）
	thresholdTime := time.Now().Add(-time.Duration(thresholdMinutes) * time.Minute).Unix()

	// 查找超过阈值时间没有更新且状态为 online 的用户
	filter := bson.M{
		"state":      "online",
		"updated_at": bson.M{"$lt": thresholdTime},
	}

	// 更新为离线状态
	update := bson.M{
		"$set": bson.M{
			"state":      "offline",
			"updated_at": time.Now().Unix(),
		},
	}

	result, err := lw.userLoginColl.UpdateMany(ctx, filter, update)
	if err != nil {
		fmt.Printf("update offline users error: %v\n", err)
		return
	}

	if result.ModifiedCount > 0 {
		fmt.Printf("Updated %d users to offline status\n", result.ModifiedCount)
	}
}
