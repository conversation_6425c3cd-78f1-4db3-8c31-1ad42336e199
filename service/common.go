// Package service defiine Sercice
package service

import (
	"ztna_server_auth/common/config"
	"ztna_server_auth/service/auth"
	"ztna_server_auth/service/cloud"
	"ztna_server_auth/service/log_writer"
	"ztna_server_auth/service/portal"
	"ztna_server_auth/service/setting"
	"ztna_server_auth/service/user_login"
)

var (
	AuthService              auth.Service
	EnterpriseSettingService setting.EnterpriseSettingService
	PortalConfigService      portal.Service
	HostBindService          cloud.Service
	LoginAccessService       log_writer.Service
	UserLoginService         user_login.Service
)

func Init(conf *config.AppConfig) {
	AuthService = auth.NewAuthService(conf)
	EnterpriseSettingService = setting.NewEnterpriseSettingService()
	PortalConfigService = portal.NewPortalConfig()
	HostBindService = cloud.NewHostBindService()
	LoginAccessService = log_writer.NewLogWriter(100)
	UserLoginService = user_login.NewUserLogin()

	// 启动离线状态检查器
	offlineInterval := conf.Service.OfflineCheckInterval
	if offlineInterval <= 0 {
		offlineInterval = 30 // 默认30分钟
	}
	LoginAccessService.StartOfflineChecker(offlineInterval)
}
