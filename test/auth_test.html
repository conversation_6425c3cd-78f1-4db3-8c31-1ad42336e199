<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth 服务测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1, h2 {
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .card {
            background-color: #fff;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="text"],
        input[type="password"] {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 14px;
        }

        button {
            background-color: #3498db;
            color: #fff;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }

        button:hover {
            background-color: #2980b9;
        }

        button.secondary {
            background-color: #7f8c8d;
        }

        button.secondary:hover {
            background-color: #6c7a7a;
        }

        button.danger {
            background-color: #e74c3c;
        }

        button.danger:hover {
            background-color: #c0392b;
        }

        .result {
            background-color: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
        }

        .success {
            color: #27ae60;
        }

        .error {
            color: #e74c3c;
        }

        .tabs {
            display: flex;
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }

        .tab {
            padding: 10px 15px;
            cursor: pointer;
            background-color: #f5f5f5;
            margin-right: 5px;
            border-radius: 4px 4px 0 0;
        }

        .tab.active {
            background-color: #3498db;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .navbar {
            background-color: #34495e;
            color: white;
            padding: 10px 20px;
            margin: -20px -20px 20px -20px;
            border-radius: 8px 8px 0 0;
        }

        .navbar h1 {
            margin: 0;
            border: none;
            color: white;
            padding: 0;
        }

        .info {
            background-color: #d5f5e3;
            border-left: 4px solid #2ecc71;
            padding: 10px 15px;
            margin-bottom: 15px;
        }

        .warning {
            background-color: #fdebd0;
            border-left: 4px solid #f39c12;
            padding: 10px 15px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
<div class="navbar">
    <h1>Auth 服务测试页面</h1>
</div>

<div class="container">
    <div class="info">
        <p>本页面用于测试ZTNA Auth服务的登录流程。确保Auth Mock服务正在运行（端口8081）。</p>
    </div>

    <div class="tabs">
        <div class="tab active" data-tab="login">普通登录</div>
        <div class="tab" data-tab="oauth">OAuth 2.0</div>
        <div class="tab" data-tab="api">API 访问</div>
        <div class="tab" data-tab="token">令牌管理</div>
        <div class="tab" data-tab="jwt">JWT 信息</div>
    </div>

    <!-- 普通登录 -->
    <div class="tab-content active" id="login-tab">
        <div class="card">
            <h2>账号密码登录</h2>
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" value="admin" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" value="123456" placeholder="输入密码">
            </div>
            <button id="login-btn">登录</button>
            <div class="result" id="login-result"></div>
        </div>
    </div>

    <!-- OAuth登录 -->
    <div class="tab-content" id="oauth-tab">
        <div class="card">
            <h2>OAuth 2.0 密码模式</h2>
            <div class="form-group">
                <label for="oauth-username">用户名</label>
                <input type="text" id="oauth-username" value="admin" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label for="oauth-password">密码</label>
                <input type="password" id="oauth-password" value="123456" placeholder="输入密码">
            </div>
            <div class="form-group">
                <label for="client-id">客户端ID</label>
                <input type="text" id="client-id" value="test_client" placeholder="输入客户端ID">
            </div>
            <div class="form-group">
                <label for="client-secret">客户端密钥</label>
                <input type="text" id="client-secret" value="test_secret" placeholder="输入客户端密钥">
            </div>
            <button id="oauth-login-btn">获取令牌</button>
            <div class="result" id="oauth-result"></div>
        </div>

        <div class="card">
            <h2>OAuth 2.0 刷新令牌</h2>
            <div class="form-group">
                <label for="refresh-token">刷新令牌</label>
                <input type="text" id="refresh-token" placeholder="输入刷新令牌">
            </div>
            <button id="refresh-btn">刷新令牌</button>
            <div class="result" id="refresh-result"></div>
        </div>
    </div>

    <!-- API访问 -->
    <div class="tab-content" id="api-tab">
        <div class="card">
            <h2>访问认证服务API</h2>
            <div class="form-group">
                <label for="access-token">访问令牌</label>
                <input type="text" id="access-token" placeholder="输入访问令牌">
            </div>
            <button id="api-access-btn">访问认证服务API</button>
            <div class="result" id="api-result"></div>
        </div>

        <div class="card">
            <h2>访问示例服务API</h2>
            <div class="info">
                <p>示例服务运行在 8082 端口，提供以下接口：</p>
                <ul>
                    <li>/health - 健康检查（无需认证）</li>
                    <li>/api/user/info - 获取用户信息（需要认证）</li>
                </ul>
            </div>
            <div class="form-group">
                <label>示例服务地址</label>
                <input type="text" id="example-service-url" value="http://localhost:8082"
                       placeholder="输入示例服务地址">
            </div>
            <button id="health-check-btn">健康检查</button>
            <button id="user-info-btn">获取用户信息</button>
            <div class="result" id="example-service-result"></div>
        </div>
    </div>

    <!-- 令牌管理 -->
    <div class="tab-content" id="token-tab">
        <div class="card">
            <h2>令牌管理</h2>
            <h3>当前令牌</h3>
            <div class="form-group">
                <label>访问令牌</label>
                <div class="result" id="current-access-token">未登录</div>
            </div>
            <div class="form-group">
                <label>刷新令牌</label>
                <div class="result" id="current-refresh-token">未登录</div>
            </div>
            <button id="logout-btn" class="danger">登出</button>
            <div class="result" id="logout-result"></div>
        </div>
    </div>

    <!-- JWT信息 -->
    <div class="tab-content" id="jwt-tab">
        <div class="card">
            <h2>JWT令牌信息</h2>
            <div class="form-group">
                <label>访问令牌载荷</label>
                <div class="result" id="jwt-access-payload">未登录</div>
            </div>
            <div class="form-group">
                <label>过期时间</label>
                <div class="result" id="jwt-expiry">未登录</div>
            </div>
            <div class="form-group">
                <label>令牌类型</label>
                <div class="result" id="jwt-token-type">未登录</div>
            </div>
            <div class="form-group">
                <label>签发时间</label>
                <div class="result" id="jwt-issued-at">未登录</div>
            </div>
            <div class="form-group">
                <label>令牌结构</label>
                <div class="info">
                    JWT由三部分组成：头部(Header)、载荷(Payload)和签名(Signature)，以点(.)分隔。
                </div>
                <div class="form-group">
                    <label>头部</label>
                    <div class="result" id="jwt-header">未登录</div>
                </div>
                <div class="form-group">
                    <label>载荷</label>
                    <div class="result" id="jwt-payload">未登录</div>
                </div>
                <div class="form-group">
                    <label>签名</label>
                    <div class="result" id="jwt-signature">未登录</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // 基础URL - 修改为ZTNA Auth服务地址
    const BASE_URL = 'http://localhost:8081';  // 根据实际部署的服务地址修改

    // 存储令牌
    let currentAccessToken = '';
    let currentRefreshToken = '';

    // 初始化页面
    document.addEventListener('DOMContentLoaded', function () {
        // 恢复存储的令牌
        const savedTokens = JSON.parse(localStorage.getItem('authTokens') || '{}');
        if (savedTokens.accessToken) {
            currentAccessToken = savedTokens.accessToken;
            currentRefreshToken = savedTokens.refreshToken;
            updateTokenDisplay();
        }

        // 标签页切换
        const tabs = document.querySelectorAll('.tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // 移除所有active类
                tabs.forEach(t => t.classList.remove('active'));
                // 隐藏所有标签内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });

                // 显示选中的标签
                tab.classList.add('active');
                const tabContent = document.getElementById(`${tab.dataset.tab}-tab`);
                if (tabContent) {
                    tabContent.classList.add('active');
                }
            });
        });

        // 普通登录
        document.getElementById('login-btn').addEventListener('click', login);

        // OAuth登录
        document.getElementById('oauth-login-btn').addEventListener('click', oauthLogin);

        // 刷新令牌
        document.getElementById('refresh-btn').addEventListener('click', refreshToken);

        // API访问
        document.getElementById('api-access-btn').addEventListener('click', accessProtectedApi);

        // 登出
        document.getElementById('logout-btn').addEventListener('click', logout);

        // 添加示例服务按钮事件监听
        document.getElementById('health-check-btn').addEventListener('click', checkExampleServiceHealth);
        document.getElementById('user-info-btn').addEventListener('click', getExampleServiceUserInfo);
    });

    // 解析JWT令牌
    function parseJwt(token) {
        try {
            // 获取JWT的三个部分
            const parts = token.split('.');
            if (parts.length !== 3) {
                return {error: "无效的JWT格式"};
            }

            // 解码头部
            const header = JSON.parse(atob(parts[0]));

            // 解码载荷
            const payload = JSON.parse(atob(parts[1]));

            return {
                header: header,
                payload: payload,
                signature: parts[2],
                raw: {
                    header: parts[0],
                    payload: parts[1],
                    signature: parts[2]
                }
            };
        } catch (error) {
            return {error: `JWT解析错误: ${error.message}`};
        }
    }

    // 普通登录
    async function login() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const resultElement = document.getElementById('login-result');

        resultElement.textContent = '登录中...';

        try {
            const response = await fetch(`${BASE_URL}/api/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            });

            const result = await response.json();

            if (response.ok) {
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = 'result success';

                // 保存令牌
                currentAccessToken = result.access_token;
                currentRefreshToken = result.refresh_token;
                updateTokenDisplay();
                saveTokens();

            } else {
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = 'result error';
            }
        } catch (error) {
            resultElement.textContent = `错误: ${error.message}`;
            resultElement.className = 'result error';
        }
    }

    // OAuth登录
    async function oauthLogin() {
        const username = document.getElementById('oauth-username').value;
        const password = document.getElementById('oauth-password').value;
        const clientId = document.getElementById('client-id').value;
        const clientSecret = document.getElementById('client-secret').value;
        const resultElement = document.getElementById('oauth-result');

        resultElement.textContent = '获取令牌中...';

        try {
            const formData = new URLSearchParams();
            formData.append('grant_type', 'password');
            formData.append('username', username);
            formData.append('password', password);
            formData.append('client_id', clientId);
            formData.append('client_secret', clientSecret);

            const response = await fetch(`${BASE_URL}/oauth/token`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Accept': 'application/json'
                },
                body: formData
            });

            const result = await response.json();

            if (response.ok) {
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = 'result success';

                // 保存令牌
                currentAccessToken = result.access_token;
                currentRefreshToken = result.refresh_token;
                updateTokenDisplay();
                saveTokens();

                // 将令牌填入刷新令牌输入框
                document.getElementById('refresh-token').value = result.refresh_token;
            } else {
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = 'result error';
            }
        } catch (error) {
            resultElement.textContent = `错误: ${error.message}`;
            resultElement.className = 'result error';
        }
    }

    // 刷新令牌
    async function refreshToken() {
        const refreshToken = document.getElementById('refresh-token').value || currentRefreshToken;
        const resultElement = document.getElementById('refresh-result');

        if (!refreshToken) {
            resultElement.textContent = '错误: 未提供刷新令牌';
            resultElement.className = 'result error';
            return;
        }

        resultElement.textContent = '刷新令牌中...';

        try {
            const response = await fetch(`${BASE_URL}/api/refresh`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    refresh_token: refreshToken
                })
            });

            const result = await response.json();

            if (response.ok) {
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = 'result success';

                // 保存新令牌
                currentAccessToken = result.access_token;
                currentRefreshToken = result.refresh_token;
                updateTokenDisplay();
                saveTokens();
            } else {
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = 'result error';
            }
        } catch (error) {
            resultElement.textContent = `错误: ${error.message}`;
            resultElement.className = 'result error';
        }
    }

    // 访问受保护的API
    async function accessProtectedApi() {
        const accessToken = document.getElementById('access-token').value || currentAccessToken;
        const resultElement = document.getElementById('api-result');

        if (!accessToken) {
            resultElement.textContent = '错误: 未提供访问令牌';
            resultElement.className = 'result error';
            return;
        }

        resultElement.textContent = '访问API中...';

        try {
            const response = await fetch(`${BASE_URL}/api/protected`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Accept': 'application/json'
                }
            });

            const result = await response.json();

            if (response.ok) {
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = 'result success';
            } else {
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = 'result error';
            }
        } catch (error) {
            resultElement.textContent = `错误: ${error.message}`;
            resultElement.className = 'result error';
        }
    }

    // 登出
    async function logout() {
        const resultElement = document.getElementById('logout-result');

        if (!currentAccessToken) {
            resultElement.textContent = '错误: 未登录';
            resultElement.className = 'result error';
            return;
        }

        resultElement.textContent = '登出中...';

        try {
            const response = await fetch(`${BASE_URL}/api/logout`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${currentAccessToken}`,
                    'Accept': 'application/json'
                }
            });

            if (response.ok) {
                const result = await response.json();
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = 'result success';

                // 清除令牌
                currentAccessToken = '';
                currentRefreshToken = '';
                updateTokenDisplay();
                localStorage.removeItem('authTokens');
            } else {
                const result = await response.json();
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = 'result error';
            }
        } catch (error) {
            resultElement.textContent = `错误: ${error.message}`;
            resultElement.className = 'result error';
        }
    }

    // 更新令牌显示
    function updateTokenDisplay() {
        const accessTokenElement = document.getElementById('current-access-token');
        const refreshTokenElement = document.getElementById('current-refresh-token');

        if (currentAccessToken) {
            accessTokenElement.textContent = currentAccessToken;
            document.getElementById('access-token').value = currentAccessToken;

            // 解析并显示JWT信息
            const jwt = parseJwt(currentAccessToken);

            if (!jwt.error) {
                // 显示JWT头部
                document.getElementById('jwt-header').textContent = JSON.stringify(jwt.header, null, 2);

                // 显示JWT载荷
                document.getElementById('jwt-payload').textContent = JSON.stringify(jwt.payload, null, 2);
                document.getElementById('jwt-access-payload').textContent = JSON.stringify(jwt.payload, null, 2);

                // 显示JWT签名（截断显示）
                document.getElementById('jwt-signature').textContent = jwt.signature.substring(0, 20) + '...';

                // 显示过期时间
                if (jwt.payload.exp) {
                    const expDate = new Date(jwt.payload.exp * 1000);
                    document.getElementById('jwt-expiry').textContent = expDate.toLocaleString();
                }

                // 显示令牌类型
                if (jwt.payload.token_type) {
                    document.getElementById('jwt-token-type').textContent = jwt.payload.token_type;
                }

                // 显示签发时间
                if (jwt.payload.iat) {
                    const iatDate = new Date(jwt.payload.iat * 1000);
                    document.getElementById('jwt-issued-at').textContent = iatDate.toLocaleString();
                }
            } else {
                // 显示JWT解析错误
                document.getElementById('jwt-header').textContent = "解析错误";
                document.getElementById('jwt-payload').textContent = jwt.error;
                document.getElementById('jwt-access-payload').textContent = jwt.error;
                document.getElementById('jwt-signature').textContent = "解析错误";
                document.getElementById('jwt-expiry').textContent = "未知";
                document.getElementById('jwt-token-type').textContent = "未知";
                document.getElementById('jwt-issued-at').textContent = "未知";
            }
        } else {
            accessTokenElement.textContent = '未登录';
            document.getElementById('jwt-header').textContent = "未登录";
            document.getElementById('jwt-payload').textContent = "未登录";
            document.getElementById('jwt-access-payload').textContent = "未登录";
            document.getElementById('jwt-signature').textContent = "未登录";
            document.getElementById('jwt-expiry').textContent = "未登录";
            document.getElementById('jwt-token-type').textContent = "未登录";
            document.getElementById('jwt-issued-at').textContent = "未登录";
        }

        if (currentRefreshToken) {
            refreshTokenElement.textContent = currentRefreshToken;
            document.getElementById('refresh-token').value = currentRefreshToken;
        } else {
            refreshTokenElement.textContent = '未登录';
        }
    }

    // 保存令牌到本地存储
    function saveTokens() {
        localStorage.setItem('authTokens', JSON.stringify({
            accessToken: currentAccessToken,
            refreshToken: currentRefreshToken
        }));
    }

    // 检查示例服务健康状态
    async function checkExampleServiceHealth() {
        const serviceUrl = document.getElementById('example-service-url').value;
        const resultElement = document.getElementById('example-service-result');

        resultElement.textContent = '检查健康状态中...';

        try {
            const response = await fetch(`${serviceUrl}/health`, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            });

            const result = await response.json();

            if (response.ok) {
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = 'result success';
            } else {
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = 'result error';
            }
        } catch (error) {
            resultElement.textContent = `错误: ${error.message}`;
            resultElement.className = 'result error';
        }
    }

    // 获取示例服务用户信息
    async function getExampleServiceUserInfo() {
        const serviceUrl = document.getElementById('example-service-url').value;
        const accessToken = currentAccessToken;
        const resultElement = document.getElementById('example-service-result');

        if (!accessToken) {
            resultElement.textContent = '错误: 未登录，请先登录获取访问令牌';
            resultElement.className = 'result error';
            return;
        }

        resultElement.textContent = '获取用户信息中...';

        try {
            const response = await fetch(`${serviceUrl}/api/user/info`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${accessToken}`,
                    'Accept': 'application/json'
                }
            });

            const result = await response.json();

            if (response.ok) {
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = 'result success';
            } else {
                resultElement.textContent = JSON.stringify(result, null, 2);
                resultElement.className = 'result error';
            }
        } catch (error) {
            resultElement.textContent = `错误: ${error.message}`;
            resultElement.className = 'result error';
        }
    }
</script>
</body>
</html> 