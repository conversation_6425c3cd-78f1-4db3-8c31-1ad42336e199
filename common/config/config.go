package config

import (
	"fmt"
	"os"
	"time"

	"rm.git/client_api/rm_common_libs.git/v2/common/configs"

	commondto "ztna_server_auth/common/dto"

	"github.com/spf13/viper"
)

var (
	v         = viper.New()
	appConfig = NewAppConfig()
)

type Service struct {
	Name                 string `mapstructure:"name"`
	Mode                 string `mapstructure:"mode"`
	Version              string `mapstructure:"version"`
	Addr                 string `mapstructure:"addr"`
	IsTestMode           bool   `mapstructure:"is_test_mode"`           // 是否为测试模式, 测试模式下, 不发送短信、不校验图形验证码
	OfflineCheckInterval int    `mapstructure:"offline_check_interval"` // 用户离线检查间隔时间（分钟）
}

type RedisConfig struct {
	Switch         bool     `mapstructure:"switch"`
	ConnectionMode int8     `mapstructure:"connection_mode"`
	Address        []string `mapstructure:"addr"`
	Password       string   `mapstructure:"password"`
	DB             int      `mapstructure:"db"`
}

type MongoConfig struct {
	Switch   bool   `mapstructure:"switch"`
	Address  string `mapstructure:"addr"`
	UserName string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	DB       string `mapstructure:"database"`
	Option   string `mapstructure:"option"`
	IsDirect bool   `mapstructure:"is_direct"`
	// ConnectionMode int8     `mapstructure:"connection_mode"`
}

// TestUser 测试用户结构
type TestUser struct {
	UserCode  string `mapstructure:"user_code"`
	Username  string `mapstructure:"username"`
	Password  string `mapstructure:"password"`
	Email     string `mapstructure:"email"`
	Phone     string `mapstructure:"phone"`
	OrgName   string `mapstructure:"org_name"`
	CreatedAt time.Time
	UpdatedAt time.Time
}

// TestUsers 测试用户配置
type TestUsers struct {
	User []TestUser `mapstructure:"user"`
}

type AppConfig struct {
	Service          Service                `mapstructure:"service"`
	Logging          map[string]interface{} `mapstructure:"logging"`
	Redis            configs.RedisConfig    `mapstructure:"redis_default"`
	Mongo            configs.MongoDBConfig  `mapstructure:"mongodb_rm_sase"`
	GrpcCloudInfo    configs.GRPCConfig     `mapstructure:"grpc_info"`
	TestUsers        TestUsers              `mapstructure:"test_users"`
	JWT              commondto.JWTConfig    `mapstructure:"jwt"`
	Sms              SmsConfig              `mapstructure:"sms"`
	Email            EmailConfig            `mapstructure:"email"`
	ThirdParty       ThirdPartyConfig       `mapstructure:"third_party"`
	Certs            configs.CertsConfig    `mapstructure:"certs"`
	EmailSubjectConf EmailSubjectConf       `mapstructure:"email_subject_conf"`
}

type ThirdPartyConfig struct {
	Address           string `mapstructure:"address"`
	LogTrackerAddress string `mapstructure:"log_tracker_address"`
	IpQueryUrl        string `mapstructure:"ip_query_url"`
}
type SmsConfig struct {
	SecretId                         string `mapstructure:"secret_id"`
	SecretKey                        string `mapstructure:"secret_key"`
	SmsSdkAppid                      string `mapstructure:"sms_sdk_appid"`
	CaptchaAppid                     uint64 `mapstructure:"captcha_appid"`
	CaptchaAppSecretKey              string `mapstructure:"captcha_app_secret_key"`
	SignName                         string `mapstructure:"sign_name"`
	VerificationCodeInterval         int    `mapstructure:"verification_code_interval"`
	VerificationCodeValidTime        int    `mapstructure:"verification_code_valid_time"`
	VerificationCodeTimesDayForPhone int    `mapstructure:"verification_code_times_day_for_phone"`
	VerificationCodeTimesDayForIp    int    `mapstructure:"verification_code_times_day_for_ip"`
}

type EmailConfig struct {
	Host                 string `mapstructure:"host"`
	Port                 int    `mapstructure:"port"`
	User                 string `mapstructure:"user"`
	Password             string `mapstructure:"password"`
	AliasName            string `mapstructure:"alias_name"`
	VerificationTemplate string `mapstructure:"verification_template"`
}

type EmailSubjectConf struct {
	Zh struct {
		Subject     string `mapstructure:"subject"`
		ProductName string `mapstructure:"product_name"`
		AliasName   string `mapstructure:"alias_name"`
	} `mapstructure:"zh"`
	En struct {
		Subject     string `mapstructure:"subject"`
		ProductName string `mapstructure:"product_name"`
		AliasName   string `mapstructure:"alias_name"`
	} `mapstructure:"en"`
}

func NewAppConfig() *AppConfig {
	return &AppConfig{}
}

func init() {

	v.AutomaticEnv()
	v.SetConfigType("toml")
	v.AddConfigPath("conf")

	// 加载业务配置
	v.SetConfigName("config.toml")

	if err := v.ReadInConfig(); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：load config failed，", err, "\033[0m")
		os.Exit(-1)
	}

	if err := v.Unmarshal(&appConfig); err != nil {
		fmt.Println("\033[5;31m", "阻断错误信息：load config failed，", err, "\033[0m")
		os.Exit(-1)
	}

	// 设置创建时间和更新时间
	now := time.Now()
	for i := range appConfig.TestUsers.User {
		appConfig.TestUsers.User[i].CreatedAt = now
		appConfig.TestUsers.User[i].UpdatedAt = now
	}
}

func Config() *AppConfig {
	return appConfig
}
