package sequence

import (
	"context"
	"errors"
	"fmt"
	"os"
	"ztna_server_auth/infrastructure/client"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Sequence struct {
	SequenceName string `bson:"sequence_name"`
	Value        int64  `bson:"value"`
}

type Repository interface {
	checkSequence() error
	GenerateUserCode() (string, error)
	GenerateOrgCode() (string, error)
}

type repository struct {
	collection *mongo.Collection
}

func NewRepository() Repository {
	r := &repository{
		collection: client.Mongo.Database.Collection("sequence"),
	}
	err := r.checkSequence()
	if err != nil {
		fmt.Printf("check sequence error: %v, sequence value sase_sequence not exist \n", err)
		os.Exit(-1)
	}
	return r
}

// 检查发号器是否存在
func (r *repository) checkSequence() error {
	filter := bson.M{"sequence_name": "sase_sequence"}
	var sequence Sequence
	err := r.collection.FindOne(context.Background(), filter).Decode(&sequence)
	if err != nil {
		return err
	}

	return nil
}

func (r *repository) GetNextSequenceID() (int64, error) {
	filter := bson.M{"sequence_name": "sase_sequence"}
	update := bson.M{"$inc": bson.M{"value": 1}}
	opts := options.FindOneAndUpdate().SetReturnDocument(options.After)
	var sequence Sequence
	err := r.collection.FindOneAndUpdate(context.Background(), filter, update, opts).Decode(&sequence)
	if err != nil {
		return 0, err
	}
	if sequence.Value > 0 {
		return sequence.Value, nil
	} else {
		return 0, errors.New("sequence value is less than 0")
	}
}

// generateDeptCode 生成部门编码
func (r *repository) GenerateDeptCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("D_%d", sequenceID), nil
}

// generateGroupCode 生成用户组编码
func (r *repository) GenerateGroupCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("G_%d", sequenceID), nil
}

// generateUserCode 生成用户编码
func (r *repository) GenerateUserCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("U_%d", sequenceID), nil
}

// generateConnectorCode 生成连接器编码
func (r *repository) GenerateConnectorCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("CO_%d", sequenceID), nil
}

// generateTagCode 生成标签编码
func (r *repository) GenerateTagCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("T_%d", sequenceID), nil
}

// generateCategoryCode 生成分类编码
func (r *repository) GenerateCategoryCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("CA_%d", sequenceID), nil
}

// generatePortalCategoryCode 生成门户分类编码
func (r *repository) GeneratePortalCategoryCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("PCA_%d", sequenceID), nil
}

// generateAdminCode 生成应用管理员编码
func (r *repository) GenerateAdminCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("A_%d", sequenceID), nil
}

// generateAppCode 生成应用编码
func (r *repository) GenerateAppCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("AP_%d", sequenceID), nil
}

// GeneratePolicyCode 生成应用策略编码
func (r *repository) GeneratePolicyCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("P_%d", sequenceID), nil
}

// GenerateSceneTemplateId 生成场景模版编码
func (r *repository) GenerateSceneTemplateId() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("ST_%d", sequenceID), nil
}

// generateOrgCode 生成组织编码
func (r *repository) GenerateOrgCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("ORG_%d", sequenceID), nil
}

// GenerateBatchNumber 生成批次号
func (r *repository) GenerateBatchNumber() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("NO_%d", sequenceID), nil
}

// GenerateRecordCode 生成审计日志记录编码
func (r *repository) GenerateLogCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("LOG_%d", sequenceID), nil
}

// GeneratePolicyNetworkRegionCode 生成网络区域编码
func (r *repository) GeneratePolicyNetworkRegionCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("PNR_%d", sequenceID), nil
}

// GeneratePolicyProcessGroupCode 生成策略流程组编码
func (r *repository) GeneratePolicyProcessGroupCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("PPG_%d", sequenceID), nil
}

// GeneratePrivateDnsCode 生成私有DNS解析编码
func (r *repository) GeneratePrivateDnsCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("PDNS_%d", sequenceID), nil
}

// GenerateAppSsoCode 生成应用SSO配置编码
func (r *repository) GenerateAppSsoCode() (string, error) {
	sequenceID, err := r.GetNextSequenceID()
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("ASSO_%d", sequenceID), nil
}
