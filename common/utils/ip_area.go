package utils

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
)

// IPGeoResponse IP地域信息响应结构
type IPGeoResponse struct {
	Error   int       `json:"error"`
	Message string    `json:"message"`
	Data    IPGeoData `json:"data"`
	Session string    `json:"session"`
}

// IPGeoData IP地域信息数据结构
type IPGeoData struct {
	Continent string `json:"continent"` // 大洲
	Country   string `json:"country"`   // 国家
	Zipcode   string `json:"zipcode"`   // 邮编
	Owner     string `json:"owner"`     // 运营商
	ISP       string `json:"isp"`       // 网络服务提供商
	Adcode    string `json:"adcode"`    // 行政区划代码
	Prov      string `json:"prov"`      // 省份
	City      string `json:"city"`      // 城市
	District  string `json:"district"`  // 区县
}

// GetIPGeoInfo 根据IP地址获取地域信息
// ip: IP地址字符串
// 返回IP地域信息和错误信息
func GetIPGeoInfo(queryUrl string, ip string) (*IPGeoData, error) {
	// 构建请求URL
	baseURL := queryUrl //https://geo.rongma.com/api/v1/geo_ip
	params := url.Values{}
	params.Add("ip", ip)

	requestURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// 发送HTTP GET请求
	resp, err := http.Get(requestURL)
	if err != nil {
		return nil, fmt.Errorf("请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析JSON响应
	var geoResp IPGeoResponse
	if err := json.Unmarshal(body, &geoResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查API返回的错误码
	if geoResp.Error != 0 {
		return nil, fmt.Errorf("API错误: %s", geoResp.Message)
	}

	return &geoResp.Data, nil
}

// GetIPGeoInfoWithRetry 带重试机制的IP地域信息获取
// ip: IP地址字符串
// maxRetries: 最大重试次数
// 返回IP地域信息和错误信息
func GetIPGeoInfoWithRetry(queryUrl string, ip string, maxRetries int) (*IPGeoData, error) {
	var lastErr error

	for i := 0; i <= maxRetries; i++ {
		data, err := GetIPGeoInfo(queryUrl, ip)
		if err == nil {
			return data, nil
		}
		lastErr = err

		// 如果不是最后一次尝试，可以在这里添加延迟
		if i < maxRetries {
			// 可以添加适当的延迟，避免频繁请求
			// time.Sleep(time.Duration(i+1) * time.Second)
		}
	}

	return nil, fmt.Errorf("重试%d次后仍然失败，最后错误: %w", maxRetries, lastErr)
}
