package utils

import (
	baseError "errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	captcha "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/captcha/v20190722"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	tencentError "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/errors"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/regions"
	sms "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/sms/v20210111"
	"strings"
	"ztna_server_auth/common/config"
	"ztna_server_auth/common/errors"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/common/utils/rmemail"
)

var conf = config.Config()

// VerifyImageCode 验证图片验证码
func VerifyImageCode(ctx *gin.Context, ticket, random string) error {

	if ticket == "" || random == "" {
		return errors.ErrSmsSend
	}

	credential := common.NewCredential(
		conf.Sms.SecretId,
		conf.Sms.SecretKey,
	)

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "captcha.tencentcloudapi.com"

	client, _ := captcha.NewClient(credential, "", cpf)

	request := captcha.NewDescribeCaptchaResultRequest()
	request.CaptchaType = common.Uint64Ptr(9)
	request.Ticket = common.StringPtr(ticket)
	request.UserIp = common.StringPtr(GetClientIp(ctx))
	request.Randstr = common.StringPtr(random)
	request.CaptchaAppId = common.Uint64Ptr(conf.Sms.CaptchaAppid)
	request.AppSecretKey = common.StringPtr(conf.Sms.CaptchaAppSecretKey)

	// 返回的resp是一个DescribeCaptchaResultResponse的实例，与请求对象对应
	response, err := client.DescribeCaptchaResult(request)
	var tencentCloudSDKError *tencentError.TencentCloudSDKError
	if baseError.As(err, &tencentCloudSDKError) {
		logs.Errorf("DescribeCaptchaResultRequest error: %s", err)
		return err
	}

	if err != nil {
		logs.Errorf("DescribeCaptchaResultRequest error: %s", err)
		return err
	}

	if *response.Response.CaptchaCode != 1 {
		return errors.ErrSmsSend
	}

	return nil
}

// SendMessage 发送短信的方法 phone || tid 模板ID || 验证码
func SendMessage(phone string, tid string, verificationCode string) error {

	var phones []string
	speedPhone := fmt.Sprintf("+86%s", phone)
	phones = append(phones, speedPhone)

	//验证码有效时长
	codeValidTime := cast.ToString(conf.Sms.VerificationCodeValidTime)
	tParamSet := []string{verificationCode, codeValidTime}

	credential := common.NewCredential(conf.Sms.SecretId, conf.Sms.SecretKey)
	cpf := profile.NewClientProfile()
	cpf.HttpProfile.ReqMethod = "POST"
	cpf.HttpProfile.Endpoint = "sms.tencentcloudapi.com"
	cpf.SignMethod = "HmacSHA1"

	client, _ := sms.NewClient(credential, regions.Beijing, profile.NewClientProfile())
	request := sms.NewSendSmsRequest()
	request.SmsSdkAppId = common.StringPtr(conf.Sms.SmsSdkAppid)
	request.SignName = common.StringPtr(conf.Sms.SignName)
	request.TemplateId = common.StringPtr(tid)
	request.TemplateParamSet = common.StringPtrs(tParamSet)
	request.PhoneNumberSet = common.StringPtrs(phones)
	request.SessionContext = common.StringPtr("")
	request.ExtendCode = common.StringPtr("")
	request.SenderId = common.StringPtr("")

	res, err := client.SendSms(request)
	// 处理异常
	var tencentCloudSDKError *tencentError.TencentCloudSDKError
	if baseError.As(err, &tencentCloudSDKError) {
		return err
	}
	// 非SDK异常，直接失败。实际代码中可以加入其他的处理。
	if err != nil {
		return err
	}

	if len(res.Response.SendStatusSet) > 0 {
		code := *res.Response.SendStatusSet[0].Code
		if strings.ToLower(code) != "ok" {
			return errors.ErrSmsSend
		}
	}

	return nil
}

// SendEmailVerificationCode 发送邮箱验证码
// 参数:
//   - email: 收件人邮箱地址
//   - verificationCode: 验证码
//
// 返回值:
//   - error: 发送失败时返回错误信息
func SendEmailVerificationCode(email, verificationCode string) error {
	// 创建邮箱配置

	data := conf.EmailSubjectConf.Zh
	//if c.GetString("language") == consts.LanguageEn {
	//	data = config.Get().EmailSubjectConf.En
	//}

	subject := data.Subject
	//productName := data.ProductName
	aliasName := data.AliasName

	// 创建邮箱发送器
	rmEmail := rmemail.NewRmEmail(rmemail.RmEmailConfig{
		Port:      conf.Email.Port,
		Host:      conf.Email.Host,
		User:      conf.Email.User,
		Password:  conf.Email.Password,
		AliasName: conf.Email.AliasName,
	})

	template := conf.Email.VerificationTemplate
	if template == "" {
		template = "您的验证码是：%s，有效期为%d分钟，请勿泄露给他人。"
	}

	content := fmt.Sprintf(template, verificationCode, conf.Sms.VerificationCodeValidTime)

	// 发送邮件
	err := rmEmail.SendEmail(
		content,
		subject,
		aliasName,
		[]string{email},
		[]string{},
		[]string{},
	)

	if err != nil {
		logs.Errorf("发送邮箱验证码失败: %v", err)
		return fmt.Errorf("发送邮箱验证码失败: %w", err)
	}

	logs.Infof("邮箱验证码发送成功: %s", email)
	return nil
}
