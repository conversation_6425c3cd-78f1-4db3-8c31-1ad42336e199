package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/medama-io/go-useragent"
	"strings"
	"time"
	"ztna_server_auth/common/clients"
	"ztna_server_auth/common/consts"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/common/utils"
	"ztna_server_auth/dto"
	"ztna_server_auth/service/log_writer"
)

func LoginMiddleware(service log_writer.Service) gin.HandlerFunc {

	return func(c *gin.Context) {

		var (
			userCode      string
			userName      string
			clientId      string
			orgCode       string
			userLoginTime int64
			platform      string
			clientIp      string
		)
		userLoginTime = time.Now().Unix()
		clientId = c.GetHeader(consts.ClientID)
		clientIp = utils.GetClientIp(c)
		platform = "Web" // 浏览器

		c.Next()

		ua := useragent.NewParser()
		agent := ua.Parse(c.Request.UserAgent())

		accessLog := dto.LoginAccess{
			Action:    "Login",
			OrgCode:   c.GetString(consts.OrgCode),
			OrgName:   c.GetString(consts.OrgName),
			ProductID: c.GetString(consts.ProductID),
			ClientID:  c.GetHeader(consts.ClientID),
			IP:        utils.GetClientIp(c),
			UserCode:  c.GetString(consts.UserCode),
			UserName:  c.GetString(consts.UserName),
			LoginTime: userLoginTime,
			Platform:  string(agent.OS()),
			Device:    string(agent.Device()),
			Browser:   string(agent.Browser()),
			UA:        c.Request.UserAgent(),
			IPPrefix:  getDataBeforeSecondDot(utils.GetClientIp(c)),
			Area: dto.Area{
				Country: "",
				City:    "",
			},
			CreateTime: time.Now().Unix()}

		logs.Infof("this request ua data is %s", c.Request.UserAgent())

		logs.Info("userCode: %s, userName: %s, orgCode: %s, userLoginTime: %d, clientId: %s, clientIp: %s, platform: %s",
			userCode, userName, orgCode, userLoginTime, clientId, clientIp, platform)
		if accessLog.ClientID == "" {
			accessLog.ClientID = c.GetString(consts.ClientID)
		}
		if accessLog.OrgCode != "" && accessLog.UserCode != "" && accessLog.ProductID != "" && accessLog.ClientID != "" {
			service.WriteLog(accessLog)
			// 日志上报的数据 - 异步执行避免影响接口响应时间
			go func(logData dto.LoginAccess) {
				if err := clients.SendLoginLog(&logData); err != nil {
					logs.Errorf("failed to send login log: %v", err)
				}
				if err := clients.Report(&logData); err != nil {
					logs.Errorf("failed to report login log: %v", err)
				}
			}(accessLog)
		}

	}
}

// getDataBeforeSecondDot 提取 IP 第二个点之前的数据
func getDataBeforeSecondDot(ip string) string {
	parts := strings.Split(ip, ".")
	if len(parts) < 2 {
		return ""
	}
	return strings.Join(parts[:2], ".")
}
