package middleware

import (
	"fmt"
	"strings"
	"time"
	"ztna_server_auth/common/clients"
	"ztna_server_auth/common/config"
	"ztna_server_auth/common/consts"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/common/utils"
	"ztna_server_auth/dto"
	"ztna_server_auth/service/log_writer"

	"github.com/gin-gonic/gin"
	"github.com/medama-io/go-useragent"
)

func LoginMiddleware(service log_writer.Service) gin.HandlerFunc {

	return func(c *gin.Context) {
		fmt.Println("this request data is login middleware,header ", c.Request.Header)

		c.Next()

		ua := useragent.NewParser()
		agent := ua.Parse(c.Request.UserAgent())

		accessLog := dto.LoginAccess{
			Action:    "Login",
			OrgCode:   c.GetString(consts.OrgCode),
			OrgName:   c.GetString(consts.OrgName),
			ProductID: c.GetString(consts.ProductID),
			ClientID:  c.<PERSON>(consts.ClientID),
			IP:        utils.GetClientIp(c),
			UserCode:  c.GetString(consts.UserCode),
			UserName:  c.GetString(consts.UserName),
			LoginTime: time.Now().Unix(),
			Platform:  string(agent.OS()),
			Device:    string(agent.Device()),
			Browser:   string(agent.Browser()),
			UA:        c.Request.UserAgent(),
			LoginType: c.GetString(consts.LoginType),
			IPPrefix:  getDataBeforeSecondDot(utils.GetClientIp(c)),
			Area: dto.Area{
				Country: "",
				City:    "",
			},
			CreateTime: time.Now().Unix()}

		logs.Infof("this request ua data is %s,acccesslog %+v", c.Request.UserAgent(), accessLog)

		if accessLog.ClientID == "" {
			accessLog.ClientID = c.GetString(consts.ClientID)
		}

		logs.Infof("this request accessLog data is %+v", accessLog)

		if accessLog.OrgCode != "" && accessLog.UserCode != "" && accessLog.ProductID != "" {
			// 日志处理和上报 - 异步执行避免影响接口响应时间
			go func(logData dto.LoginAccess) {
				//  根据IP获取地域
				iPData, err := utils.GetIPGeoInfo(config.Config().ThirdParty.IpQueryUrl, logData.IP)
				if err != nil {
					logs.Errorf("get ip data error: %v", err)
				} else {
					logData.Area = dto.Area{
						Country: iPData.Country,
						City:    iPData.City,
						Prov:    iPData.Prov,
					}
				}
				// 写入本地日志
				service.WriteLog(logData)

				// 发送登录日志到远程服务
				if err := clients.SendLoginLog(&logData); err != nil {
					logs.Errorf("failed to send login log: %v", err)
				}

				// 上报登录数据
				if err := clients.Report(&logData); err != nil {
					logs.Errorf("failed to report login log: %v", err)
				}
			}(accessLog)
		}

	}
}

// getDataBeforeSecondDot 提取 IP 第二个点之前的数据
func getDataBeforeSecondDot(ip string) string {
	parts := strings.Split(ip, ".")
	if len(parts) < 2 {
		return ""
	}
	return strings.Join(parts[:2], ".")
}
