package auth

import (
	"net/http"
	"strings"
	"ztna_server_auth/common/consts"

	"ztna_server_auth/common/errors"
	"ztna_server_auth/dto"

	"ztna_server_auth/common/logs"
	"ztna_server_auth/service"

	"github.com/gin-gonic/gin"
)

// Middleware  认证中间件
type Middleware struct{}

// NewAuthMiddleware 创建新的认证中间件
func NewAuthMiddleware() *Middleware {
	return &Middleware{}
}

// Authenticate 认证中间件函数
func (m *Middleware) Authenticate() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取认证令牌
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			logs.PInfof("No authorization header provided")
			return
		}

		// 解析令牌
		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			logs.PInfof("Invalid authorization header format")
			return
		}

		accessToken := parts[1]

		// 验证令牌
		user, err := service.AuthService.ValidateToken(c.Request.Context(), accessToken)
		if err != nil {
			logs.PErrorf("Token validation failed: %v", err)
			return
		}

		// 将用户信息添加到 Gin 上下文
		c.Set("user", user)
		c.Set("access_token", accessToken)
		c.Next()
	}
}

// CORS 中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置 CORS 头
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization,client_id,rm-ticket,X-Client-Id")

		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusOK)
			return
		}

		c.Next()
	}
}

// RequestLogger 请求日志中间件
func RequestLogger() gin.HandlerFunc {
	return func(c *gin.Context) {
		logs.PInfof("[%s] %s %s", c.Request.Method, c.Request.URL.Path, c.ClientIP())
		c.Next()
	}
}

func GetUserInfo(c *gin.Context) (*dto.AuthUserInfo, error) {
	user, exists := c.Get("user")
	if !exists {
		return nil, errors.Unauthorized
	}
	userInfo, ok := user.(*dto.AuthUserInfo)
	if !ok {
		return nil, errors.InvalidToken
	}
	return userInfo, nil
}

func GetAccessTokenFromCtx(ctx *gin.Context) string {
	return ctx.GetString("access_token")
}

func SetUserLoginInfoToContext(c *gin.Context, userInfo *dto.TokenResponse) {

	c.Set(consts.UserCode, userInfo.UserCode)
	c.Set(consts.UserName, userInfo.Username)
	c.Set(consts.OrgCode, userInfo.OrgCode)
	c.Set(consts.ProductID, userInfo.ProductID)
	c.Set(consts.LoginType, userInfo.LoginType)
	c.Set(consts.OrgName, userInfo.OrgName)
}
