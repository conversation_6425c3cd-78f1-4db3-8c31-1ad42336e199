// Package controller  define router
package controller

import (
	"github.com/gin-gonic/gin"
	"ztna_server_auth/common/logs"
	"ztna_server_auth/controller/auth"
	"ztna_server_auth/controller/middleware"
)

func RegisterRoutes(r *gin.Engine) {
	logs.PInfof("RegisterRoutes: start")
	r.Use(gin.Recovery())
	r.Use(auth.RequestLogger())
	r.Use(auth.CORS())
	// 创建认证中间件
	authMiddleware := auth.NewAuthMiddleware()

	v1 := r.Group("/sase/auth/api/v1")
	{

		authController := auth.NewAuthController()
		// OAuth 2.0 端点
		v1.GET("/oauth/authorize", authController.HandleAuthorize)
		v1.POST("/oauth/token", authController.HandleToken)

		// 登录端点
		v1.POST("/login", authController.HandleLogin, middleware.LoginMiddleware())
		v1.POST("/logout", authController.HandleLogout)
		// 刷新令牌端点
		v1.POST("/refresh", authController.HandleRefreshToken)

		// 第三方登录端点
		v1.GET("/login/third-party", authController.HandleThirdPartyLogin)
		v1.GET("/login/third-party/dingtalk", authController.HandleDingTalkLogin, middleware.LoginMiddleware())

		v1.POST("/login/third-party/ldap", authController.HandleLdapLogin, middleware.LoginMiddleware())
		v1.POST("/third-party/ldap_account/login", authController.HandleLdapDomainAccountLogin, middleware.LoginMiddleware())

		v1.POST("/login/guest", authController.HandleGuestLogin, middleware.LoginMiddleware())

		v1.POST("/enterprise/authenticate", authController.HandleAuthenticate)       // 企业密码校验
		v1.POST("/enterprise/login/setting", authController.HandleEnterpriseSetting) // 企业的登录设置

		v1.POST("/login/phone", authController.HandlePhoneLogin, middleware.LoginMiddleware()) // 手机号验证码登录

		v1.POST("/captcha", authController.HandleSendCaptcha) // 发送验证码

		v1.POST("/reset/password", authController.HandleResetPwd) // 忘记密码

		// 根据productId获取组织列表
		v1.POST("/org/list", authController.HandleOrgList)
		v1.POST("/orgs", authController.HandleOrgs)

		v1.POST("/org/:org_code/login_type", authController.HandleLoginList) // 登录的方式的列表

		login := v1.Group("", authMiddleware.Authenticate()) // 登录状态下的接口
		{
			login.POST("/reset_password", authController.HandleLoginResetPwd)                            // 登录状态下的修改密码
			login.GET("/validate", authController.ValidateTokenEndpoint)                                 // 验证token端点
			login.GET("/check_token", authController.HandleCheckToken, middleware.CheckUserLoginState()) // 验证token·

		}

	}

	logs.PInfof("RegisterRoutes: end")
}
